<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7; ">
        <div ref="searchContentRef" style="margin-bottom: 16px; background-color: #fff; padding: 16px">
            <searchBox
                :searchOptionKey="'TASK_MANAGEMENT'"
                :customConfig="customSearchOptionConifg"
                @updateSearchParams="updateSearchParams"
            >
            </searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div ref="actionBarContentRef" class="b-margin-16">
                <a class="color-three-grey font-14">提示：</a>
                <a class="color-two-grey font-14">下载链接有效期为</a>
                <a class="color-red font-14 font-bold"> 3 </a>
                <a class="color-two-grey font-14">天，逾期将自动失效</a>
            </div>
            <el-table
                :data="tagList"
                style="width: 100%"
                :height="tableHeight+'px'"
                empty-text="暂无数据"
                v-loading="tableLoading"
                show-overflow-tooltip
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <template v-if="!tableLoading" #empty>
                    <div class="display-flex flex-column top-bottom-center" style="height: 70vh;">
                        <img class="w-260 h-260 " src="@/assets/images/customer-public-no-data.png" alt="暂无数据">
                        <div class="font-first-title-unactive color-two-grey">暂无数据</div>
                    </div>
                </template>
                <el-table-column label="任务名称" prop="taskName" min-width="300"></el-table-column>
                <el-table-column label="创建时间" prop="createTime" width="200">
                    <template #default="scope">
                        <div>
                            {{
                                scope.row.createTime
                                    ? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="完成时间" prop="updateTime" width="200">
                    <template #default="scope">
                        <div>
                            {{
                                scope.row.updateTime
                                    ? moment(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'
                            }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="类型" prop="taskTypeStr" min-width="200">
                </el-table-column>
                <el-table-column label="状态" prop="status" min-width="200">
                    <template #default="scope">
                        <el-tag type="primary" v-if="scope.row.status === 0">进行中</el-tag>
                        <el-tag type="success" v-if="scope.row.status === 1">已完成</el-tag>
                        <el-tag type="info" v-if="scope.row.status === 2">已过期</el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="150px"
                >
                    <template #default="scope">
                        <div>
                            <a v-if="scope.row.status === 1" class="pointer color-blue" @click="download(scope.row)">下载</a>
                        </div>  
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>    
        </div>
    </div>
</template>

<script lang='ts' setup>
import { getCurrentInstance, ref, reactive, onMounted, onBeforeMount, } from 'vue'
import searchBox from '@/components/common/SearchBox.vue'
import systemService from '@/service/systemService'
import type { ITaskListRequest, ITaskListResponseItem } from '@/types/task'

const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
const tableHeight = ref(500)
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - actionBarContentRef.value.clientHeight - 32 - 16 - 16 - 16 - 16 - 16
    }
}
const tableLoading = ref<boolean>(false)

const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

const queryParams = ref<ITaskListRequest>({
    page: pageInfo.page,
    pageSize: pageInfo.pageSize,
})

const updateSearchParams = (params: ITaskListRequest) => {
    // console.log('updateSearchParams', params)
    queryParams.value = params
    search()
}
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const tagList = ref<ITaskListResponseItem[]>([])

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    search()
}

const search = () => {
    tableLoading.value = true
    queryParams.value.page = pageInfo.page
    queryParams.value.pageSize = pageInfo.pageSize
    systemService.taskList(queryParams.value).then((res) => {
        // console.log('123123123123',res)
        tagList.value = res.data
        pageInfo.total = res.total
    }).finally(() => {
        tableLoading.value = false
    })
    
}

const getFileUrl = (name: string) => {
    const Url = `https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/`
    // console.log('Url1111', `${Url}${name}`)
    return `${Url}${name}`
}

const download = (row: ITaskListResponseItem) => {
    console.log(row)
    let fileUrl = getFileUrl(row.taskInfo.name)
    if (fileUrl) {
        window.location.href = fileUrl
    }
}

type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: number | string
    }>
}
const customSearchOptionConifg = ref<CustomConfig>({})
const getSearchOptions = async () => {
    const res = await systemService.taskEnums()
    // console.log('12312313',res.data)
    customSearchOptionConifg.value = {
        taskType:res.data.taskType,
        status:res.data.taskStatus
    }
}
onBeforeMount(() => {
    getSearchOptions()
})
onMounted(() => {
    search()
    getTableHeight()
})
</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>